import { useState, useEffect } from "react";
import PropTypes from 'prop-types';

import { useDesignSpace } from "@contexts/DesignSpaceContext";
import "./DesignSpaceOverrides.css"; // Import custom CSS to override transparency
import "./RotationHandles.css"; // Import custom CSS for rotation handles
import "./cursors.css"; // Import professional cursors
import "./MobileDesignSpace.css"; // Import mobile-specific styles

import AlignmentContainer from "./components/AlignmentContainer";
import AlignmentControl from "./components/AlignmentControl";
import DuplicateControl from "./components/DuplicateControl";
import DeleteControl from "./components/DeleteControl";
import ResizeInputs from "./components/ResizeInputs";
import TypeControl from "./components/TypeControl";
import Element from "./components/Element";
import CanvaToolbar from "./components/CanvaToolbar";
import HelpGuide from "./components/HelpGuide";
import ImageEditSidebar from "./components/ImageEditSidebar";
import AIToolsHub from "./components/AIToolsHub";
import DesignSpaceBackground from "./components/DesignSpaceBackground";
import LeftSidebar from "./components/LeftSidebar";
import { FaRobot } from 'react-icons/fa';
import { FiHelpCircle, FiChevronRight, FiChevronLeft } from 'react-icons/fi';
import { RiScissorsCutFill } from 'react-icons/ri';
import { motion } from 'framer-motion';
import ColorPicker from "./components/ColorPicker";
import { MdOutlineColorLens } from 'react-icons/md';

const DesignSpace = ({ updateTemplateData, design }) => {
    const [alignmentLines, setAlignmentLines] = useState({ vertical: null, horizontal: null });
    const [toolbarPosition, setToolbarPosition] = useState(null);
    const [toolbarClasses, setToolbarClasses] = useState('');

    // Mobile responsiveness states
    const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

    const {
        selectedIds, setSelectedIds,
        updateElement, designSpaceRef,
        elements, setElements, isMultiSelectActive,
        setSelectedElement, cardType,
        canvasBackgroundStyle,
        bringToFront, sendToBack,
        zoomLevel
    } = useDesignSpace();

    const [showImageSidebar, setShowImageSidebar] = useState(false);
    const [showAITools, setShowAITools] = useState(false);

    // Add state for rotation mode
    const [isRotationMode, setIsRotationMode] = useState(false);

    // Add state for clipboard
    const [clipboard, setClipboard] = useState([]);

    // Add state for color picker target
    const [colorPickerTargetId, setColorPickerTargetId] = useState(null);
    const [colorPickerPosition, setColorPickerPosition] = useState({ top: 100, left: 100 });

    // Mobile detection useEffect
    useEffect(() => {
        const handleResize = () => {
            const mobileView = window.innerWidth < 768;
            setIsMobile(mobileView);
        };

        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    // Mobile touch gesture support
    useEffect(() => {
        if (!isMobile) return;

        let touchStartX = 0;
        let touchStartY = 0;
        let touchStartTime = 0;

        const handleTouchStart = (e) => {
            touchStartX = e.touches[0].clientX;
            touchStartY = e.touches[0].clientY;
            touchStartTime = Date.now();
        };

        const handleTouchEnd = (e) => {
            const touchEndX = e.changedTouches[0].clientX;
            const touchEndY = e.changedTouches[0].clientY;
            const touchEndTime = Date.now();

            const deltaX = touchEndX - touchStartX;
            const deltaY = touchEndY - touchStartY;
            const deltaTime = touchEndTime - touchStartTime;

            // Detect swipe gestures
            if (Math.abs(deltaX) > 50 && deltaTime < 300) {
                if (deltaX > 0) {
                    // Swipe right - could be used for navigation
                    console.log('Swipe right detected');
                } else {
                    // Swipe left - could be used for navigation
                    console.log('Swipe left detected');
                }
            }

            if (Math.abs(deltaY) > 50 && deltaTime < 300) {
                if (deltaY > 0) {
                    // Swipe down - could close mobile panels
                    console.log('Swipe down detected');
                } else {
                    // Swipe up - could open mobile panels
                    console.log('Swipe up detected');
                }
            }
        };

        const designSpace = designSpaceRef.current;
        if (designSpace) {
            designSpace.addEventListener('touchstart', handleTouchStart, { passive: true });
            designSpace.addEventListener('touchend', handleTouchEnd, { passive: true });

            return () => {
                designSpace.removeEventListener('touchstart', handleTouchStart);
                designSpace.removeEventListener('touchend', handleTouchEnd);
            };
        }
    }, [isMobile, designSpaceRef]);

    // Helper function to get mouse position relative to design space, considering zoom
    const getRelativeMousePosition = (e, designSpaceRect, zoomLevel) => {
        // zoomLevel is a percentage (e.g., 100, 120, 80)
        const scale = zoomLevel / 100;
        const x = (e.clientX - designSpaceRect.left) / scale;
        const y = (e.clientY - designSpaceRect.top) / scale;
        return { x, y };
    };

    const handleMouseDown = (e, id, resizeCorner = null) => {
        e.stopPropagation();

        // Get design space dimensions
        const designSpace = designSpaceRef.current;
        const designSpaceRect = designSpace.getBoundingClientRect();

        // Get initial mouse position (relative to design space, considering zoom)
        const { x: startX, y: startY } = getRelativeMousePosition(e, designSpaceRect, zoomLevel);

        // Get the element from the elements array
        const elementIndex = elements.findIndex(el => el.id === id);
        if (elementIndex === -1) return;

        // Make a copy of the element to work with
        const element = {...elements[elementIndex]};

        // Store initial element position and size
        const initialX = element.x;
        const initialY = element.y;
        const initialWidth = element.width;
        const initialHeight = element.height;

        let rafId = null;
        let lastDeltaX = 0;
        let lastDeltaY = 0;
        let isResizing = resizeCorner !== null;

        const updateElementPosition = () => {
            // Create a copy of the elements array
            const newElements = [...elements];

            // Handle resizing
            if (isResizing) {
                let newWidth = initialWidth;
                let newHeight = initialHeight;
                let newX = initialX;
                let newY = initialY;

                // Calculate new dimensions based on the corner being dragged
                if (resizeCorner.includes("right")) {
                    newWidth = Math.max(20, initialWidth + lastDeltaX);
                }
                if (resizeCorner.includes("left")) {
                    const widthChange = lastDeltaX;
                    newWidth = Math.max(20, initialWidth - widthChange);
                    // Keep the right edge fixed
                    newX = initialX + widthChange;
                }
                if (resizeCorner.includes("bottom")) {
                    newHeight = Math.max(20, initialHeight + lastDeltaY);
                }
                if (resizeCorner.includes("top")) {
                    const heightChange = lastDeltaY;
                    newHeight = Math.max(20, initialHeight - heightChange);
                    // Keep the bottom edge fixed
                    newY = initialY + heightChange;
                }

                // Apply constraints to keep element within design space
                if (resizeCorner.includes("left")) {
                    newX = Math.max(0, Math.min(newX, designSpaceRect.width - newWidth));
                    // Recalculate width if X position is constrained
                    if (newX === 0) {
                        newWidth = initialWidth + initialX;
                    } else if (newX === designSpaceRect.width - newWidth) {
                        newWidth = designSpaceRect.width - newX;
                    }
                }
                if (resizeCorner.includes("right")) {
                    newWidth = Math.min(newWidth, designSpaceRect.width - initialX);
                }
                if (resizeCorner.includes("top")) {
                    newY = Math.max(0, Math.min(newY, designSpaceRect.height - newHeight));
                    // Recalculate height if Y position is constrained
                    if (newY === 0) {
                        newHeight = initialHeight + initialY;
                    } else if (newY === designSpaceRect.height - newHeight) {
                        newHeight = designSpaceRect.height - newY;
                    }
                }
                if (resizeCorner.includes("bottom")) {
                    newHeight = Math.min(newHeight, designSpaceRect.height - initialY);
                }

                // Update the element in the array
                newElements[elementIndex] = {
                    ...element,
                    x: newX,
                    y: newY,
                    width: newWidth,
                    height: newHeight
                };

                // Update React state
                setElements(newElements);
            }
            // Handle moving only if not resizing
            else {
                // Calculate new position
                const newX = Math.max(0, Math.min(initialX + lastDeltaX, designSpaceRect.width - element.width));
                const newY = Math.max(0, Math.min(initialY + lastDeltaY, designSpaceRect.height - element.height));

                // Get the DOM element for direct manipulation
                const domElement = document.querySelector(`[data-element-id="${element.id}"]`);
                if (domElement) {
                    // Use direct DOM manipulation for smoother movement
                    domElement.style.left = `${newX}px`;
                    domElement.style.top = `${newY}px`;

                    // Update toolbar position in real-time
                    const elementControls = domElement.querySelector('.element-controls');
                    if (elementControls) {
                        // Get the element's current position relative to the design space
                        const elementRect = domElement.getBoundingClientRect();
                        const elementTop = elementRect.top - designSpaceRect.top;
                        const elementRight = designSpaceRect.right - elementRect.right;
                        const elementLeft = elementRect.left - designSpaceRect.left;
                        const elementWidth = elementRect.width;
                        const elementHeight = elementRect.height;
                        
                        // Define the threshold for edge detection (in pixels)
                        const edgeThreshold = 50;
                        
                        // Define the distance between toolbar and element
                        const toolbarDistance = 60;
                        
                        // Determine if element is near edges
                        const isNearTop = elementTop < edgeThreshold;
                        const isNearRight = elementRight < edgeThreshold;
                        const isNearLeft = elementLeft < edgeThreshold;
                        
                        // Remove existing edge classes
                        elementControls.classList.remove('top-edge', 'right-edge', 'left-edge');
                        
                        // Calculate toolbar position based on element dimensions and position
                        if (isNearRight) {
                            // Position toolbar on the left side
                            elementControls.style.left = `-${toolbarDistance}px`;
                            elementControls.style.top = `${elementHeight / 2}px`;
                            elementControls.style.transform = 'translateY(-50%)';
                            elementControls.style.marginLeft = '0';
                            elementControls.style.marginRight = '0';
                            elementControls.style.flexDirection = 'column';
                            elementControls.style.alignItems = 'center';
                            elementControls.style.justifyContent = 'center';
                            elementControls.classList.add('right-edge');
                        } else if (isNearLeft) {
                            // Position toolbar on the right side with vertical alignment
                            elementControls.style.left = `${elementWidth + 20}px`;
                            elementControls.style.top = `${elementHeight / 2}px`;
                            elementControls.style.transform = 'translateY(-50%)';
                            elementControls.style.marginLeft = '0';
                            elementControls.style.marginRight = '0';
                            elementControls.style.flexDirection = 'column';
                            elementControls.style.alignItems = 'center';
                            elementControls.style.justifyContent = 'center';
                            elementControls.classList.add('left-edge');
                        } else {
                            // Position toolbar based on element's vertical position
                            if (isNearTop) {
                                // Position toolbar on the bottom when near top edge
                                elementControls.style.top = 'auto';
                                elementControls.style.bottom = `-${toolbarDistance}px`;
                                elementControls.style.left = `${elementWidth / 2}px`;
                                elementControls.style.transform = 'translateX(-50%)';
                                elementControls.style.flexDirection = 'row';
                                elementControls.style.alignItems = 'center';
                            } else {
                                // Position toolbar on the top when not near top edge
                                elementControls.style.top = `-${toolbarDistance}px`;
                                elementControls.style.bottom = 'auto';
                                elementControls.style.left = `${elementWidth / 2}px`;
                                elementControls.style.transform = 'translateX(-50%)';
                                elementControls.style.flexDirection = 'row';
                                elementControls.style.alignItems = 'center';
                            }
                        }

                        // Ensure toolbar is visible and outside the element
                        elementControls.style.zIndex = '1000';
                        elementControls.style.pointerEvents = 'auto';
                        elementControls.style.position = 'absolute';
                        elementControls.style.display = 'flex';
                    }
                }

                // Update the element in the array
                newElements[elementIndex] = {
                    ...element,
                    x: newX,
                    y: newY
                };

                // Update React state
                setElements(newElements);

                // Show alignment lines
                showAlignmentLines(newX, newY, element, designSpaceRect);
            }

            rafId = null;
        };

        const handleMouseMove = (e) => {
            // احسب الموضع الجديد للماوس مع مراعاة الزوم
            const { x: currentX, y: currentY } = getRelativeMousePosition(e, designSpaceRect, zoomLevel);
            lastDeltaX = currentX - startX;
            lastDeltaY = currentY - startY;
            if (!rafId) {
                rafId = requestAnimationFrame(updateElementPosition);
            }
        };

        const handleMouseUp = () => {
            // Clear alignment lines
            setAlignmentLines({ vertical: null, horizontal: null });

            // Remove event listeners
            document.removeEventListener("mousemove", handleMouseMove);
            document.removeEventListener("mouseup", handleMouseUp);

            // Reset resize flag
            isResizing = false;
        };

        document.addEventListener("mousemove", handleMouseMove);
        document.addEventListener("mouseup", handleMouseUp);
    };

    // Handle rotation of elements
    const handleRotationStart = (e, id) => {
        e.stopPropagation();
        if (!selectedIds.includes(id)) {
            return;
        }
        const element = elements.find(el => el.id === id);
        if (!element) return;
        const domElement = document.querySelector(`[data-element-id="${id}"]`);
        const elementRect = domElement.getBoundingClientRect();
        const designSpace = designSpaceRef.current;
        const designSpaceRect = designSpace.getBoundingClientRect();
        const scale = zoomLevel / 100;
        // احسب مركز العنصر مع مراعاة الزوم
        const centerX = (elementRect.left - designSpaceRect.left) / scale + elementRect.width / (2 * scale);
        const centerY = (elementRect.top - designSpaceRect.top) / scale + elementRect.height / (2 * scale);
        // احسب زاوية البداية
        const { x: mouseX, y: mouseY } = getRelativeMousePosition(e, designSpaceRect, zoomLevel);
        const startAngle = Math.atan2(mouseY - centerY, mouseX - centerX) * (180 / Math.PI);
        const initialRotation = element.rotation || 0;
        let lastClientX = 0;
        let lastClientY = 0;
        let rotationRafId = null;
        let isRotating = true;
        const updateElementRotation = () => {
            if (!isRotating || !selectedIds.includes(id)) {
                isRotating = false;
                if (rotationRafId) {
                    cancelAnimationFrame(rotationRafId);
                    rotationRafId = null;
                }
                return;
            }
            // احسب الموضع الحالي للماوس مع مراعاة الزوم
            const { x: moveX, y: moveY } = getRelativeMousePosition({ clientX: lastClientX, clientY: lastClientY }, designSpaceRect, zoomLevel);
            const newAngle = Math.atan2(moveY - centerY, moveX - centerX) * (180 / Math.PI);
            let rotationDelta = newAngle - startAngle;
            const newRotation = initialRotation + rotationDelta;
            if (domElement && selectedIds.includes(id)) {
                domElement.style.transform = `rotate(${newRotation}deg)`;
                domElement.style.transformOrigin = 'center center';
                domElement.style.willChange = 'transform';
                domElement._pendingRotation = newRotation;
            }
            rotationRafId = null;
        };
        const handleRotationMove = (e) => {
            lastClientX = e.clientX;
            lastClientY = e.clientY;
            if (!rotationRafId) {
                rotationRafId = requestAnimationFrame(updateElementRotation);
            }
        };
        const handleRotationEnd = () => {
            isRotating = false;
            if (rotationRafId) {
                cancelAnimationFrame(rotationRafId);
                rotationRafId = null;
            }

            // Remove event listeners
            document.removeEventListener('mousemove', handleRotationMove);
            document.removeEventListener('mouseup', handleRotationEnd);

            // Apply pending rotation to React state only if element is still selected
            if (selectedIds.includes(id)) {
                const domElementFinal = document.querySelector(`[data-element-id="${id}"]`);
                if (domElementFinal && domElementFinal._pendingRotation !== undefined) {
                    updateElement(id, { rotation: domElementFinal._pendingRotation });
                    delete domElementFinal._pendingRotation;
                }
            }
        };

        // Add event listeners
        document.addEventListener('mousemove', handleRotationMove);
        document.addEventListener('mouseup', handleRotationEnd);

        // Add cleanup function to remove event listeners when element is deselected
        const cleanup = () => {
            isRotating = false;
            if (rotationRafId) {
                cancelAnimationFrame(rotationRafId);
                rotationRafId = null;
            }
            document.removeEventListener('mousemove', handleRotationMove);
            document.removeEventListener('mouseup', handleRotationEnd);
        };

        // Store cleanup function on the element
        const domElementForCleanup = document.querySelector(`[data-element-id="${id}"]`);
        if (domElementForCleanup) {
            domElementForCleanup._rotationCleanup = cleanup;
        }
    };

    // Add effect to handle deselection
    useEffect(() => {
        return () => {
            // Cleanup rotation handlers for all elements
            elements.forEach(el => {
                const domElement = document.querySelector(`[data-element-id="${el.id}"]`);
                if (domElement && domElement._rotationCleanup) {
                    domElement._rotationCleanup();
                    delete domElement._rotationCleanup;
                }
            });
        };
    }, [selectedIds]);

    // Handle free rotation from corners
    const handleFreeRotationStart = (e, id) => {
        e.stopPropagation();
        const element = elements.find(el => el.id === id);
        if (!element) return;
        const elementNode = document.querySelector(`[data-element-id="${id}"]`);
        if (!elementNode) return;
        const elementRect = elementNode.getBoundingClientRect();
        const designSpace = designSpaceRef.current;
        const designSpaceRect = designSpace.getBoundingClientRect();
        const scale = zoomLevel / 100;
        // احسب مركز العنصر مع مراعاة الزوم
        const centerX = (elementRect.left - designSpaceRect.left) / scale + elementRect.width / (2 * scale);
        const centerY = (elementRect.top - designSpaceRect.top) / scale + elementRect.height / (2 * scale);
        // احسب زاوية البداية
        const { x: mouseX, y: mouseY } = getRelativeMousePosition(e, designSpaceRect, zoomLevel);
        const initialAngle = Math.atan2(mouseY - centerY, mouseX - centerX);
        const initialRotation = element.rotation || 0;
        // Create enhanced rotation visualization
        const rotationContainer = document.createElement('div');
        rotationContainer.className = 'rotation-visualization';
        rotationContainer.style.position = 'fixed';
        rotationContainer.style.left = '0';
        rotationContainer.style.top = '0';
        rotationContainer.style.width = '100%';
        rotationContainer.style.height = '100%';
        rotationContainer.style.pointerEvents = 'none';
        rotationContainer.style.zIndex = '9999';

        // Create rotation center indicator
        const centerIndicator = document.createElement('div');
        centerIndicator.className = 'rotation-center';
        centerIndicator.style.position = 'absolute';
        centerIndicator.style.width = '12px';
        centerIndicator.style.height = '12px';
        centerIndicator.style.borderRadius = '50%';
        centerIndicator.style.backgroundColor = 'rgba(0, 196, 204, 0.8)';
        centerIndicator.style.border = '2px solid white';
        centerIndicator.style.boxShadow = '0 0 8px rgba(0, 0, 0, 0.3)';
        centerIndicator.style.left = `${centerX - 6}px`;
        centerIndicator.style.top = `${centerY - 6}px`;
        centerIndicator.style.zIndex = '2';
        centerIndicator.style.transition = 'transform 0.2s ease';
        centerIndicator.style.transform = 'scale(1)';

        // Create rotation line
        const rotationLine = document.createElement('div');
        rotationLine.className = 'rotation-line';
        rotationLine.style.position = 'absolute';
        rotationLine.style.height = '3px';
        rotationLine.style.backgroundColor = '#00c4cc';
        rotationLine.style.transformOrigin = 'left center';
        rotationLine.style.left = `${centerX}px`;
        rotationLine.style.top = `${centerY}px`;
        rotationLine.style.width = `${Math.sqrt(Math.pow(mouseX - centerX, 2) + Math.pow(mouseY - centerY, 2))}px`;
        rotationLine.style.transform = `rotate(${Math.atan2(mouseY - centerY, mouseX - centerX) * (180 / Math.PI)}deg)`;
        rotationLine.style.boxShadow = '0 0 8px rgba(0, 196, 204, 0.5)';
        rotationLine.style.zIndex = '1';
        rotationLine.style.opacity = '0.8';

        // Create rotation handle at the end of the line
        const rotationHandle = document.createElement('div');
        rotationHandle.className = 'rotation-end-handle';
        rotationHandle.style.position = 'absolute';
        rotationHandle.style.width = '16px';
        rotationHandle.style.height = '16px';
        rotationHandle.style.borderRadius = '50%';
        rotationHandle.style.backgroundColor = '#00c4cc';
        rotationHandle.style.border = '2px solid white';
        rotationHandle.style.boxShadow = '0 0 8px rgba(0, 0, 0, 0.3)';
        rotationHandle.style.left = `${mouseX - 8}px`;
        rotationHandle.style.top = `${mouseY - 8}px`;
        rotationHandle.style.zIndex = '2';
        rotationHandle.style.transition = 'transform 0.2s ease';
        rotationHandle.style.transform = 'scale(1)';

        // Create rotation angle indicator
        const angleIndicator = document.createElement('div');
        angleIndicator.className = 'rotation-angle';
        angleIndicator.style.position = 'absolute';
        angleIndicator.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
        angleIndicator.style.color = 'white';
        angleIndicator.style.padding = '4px 8px';
        angleIndicator.style.borderRadius = '4px';
        angleIndicator.style.fontSize = '12px';
        angleIndicator.style.fontWeight = 'bold';
        angleIndicator.style.left = `${centerX + 20}px`;
        angleIndicator.style.top = `${centerY - 30}px`;
        angleIndicator.style.zIndex = '3';
        angleIndicator.style.opacity = '0.9';
        angleIndicator.textContent = '0°';

        // Add all elements to the container
        rotationContainer.appendChild(centerIndicator);
        rotationContainer.appendChild(rotationLine);
        rotationContainer.appendChild(rotationHandle);
        rotationContainer.appendChild(angleIndicator);

        // Add container to the document
        document.body.appendChild(rotationContainer);

        let lastClientX = 0;
        let lastClientY = 0;
        let freeRotationRafId = null;
        const updateFreeRotation = () => {
            // احسب الموضع الحالي للماوس مع مراعاة الزوم
            const { x: moveX, y: moveY } = getRelativeMousePosition({ clientX: lastClientX, clientY: lastClientY }, designSpaceRect, zoomLevel);
            const newAngle = Math.atan2(moveY - centerY, moveX - centerX);
            const angleDiff = (newAngle - initialAngle) * (180 / Math.PI);
            const newRotation = initialRotation + angleDiff;
            // Format rotation angle for display
            const formattedAngle = Math.round(newRotation % 360);
            angleIndicator.textContent = `${formattedAngle}°`;

            // Update rotation line visual
            const lineLength = Math.sqrt(Math.pow(moveX - centerX, 2) + Math.pow(moveY - centerY, 2));
            rotationLine.style.width = `${lineLength}px`;
            rotationLine.style.transform = `rotate(${newAngle * (180 / Math.PI)}deg)`;
            rotationLine.style.willChange = 'transform, width';

            // Update rotation handle position
            rotationHandle.style.left = `${moveX - 8}px`;
            rotationHandle.style.top = `${moveY - 8}px`;

            // Add pulse effect to handle
            rotationHandle.style.transform = 'scale(1.2)';
            setTimeout(() => {
                if (rotationHandle.parentNode) { // Check if still in DOM
                    rotationHandle.style.transform = 'scale(1)';
                }
            }, 150);

            // Use direct DOM manipulation with hardware acceleration for smoother performance
            const element = document.querySelector(`[data-element-id="${id}"]`);
            if (element) {
                element.style.transform = `rotate(${newRotation}deg)`;
                element.style.transformOrigin = 'center center';
                element.style.willChange = 'transform';

                // Store the pending update to apply when rotation ends
                element._pendingRotation = newRotation;
            }

            freeRotationRafId = null;
        };

        const handleRotationMove = (e) => {
            // Store latest mouse position
            lastClientX = e.clientX;
            lastClientY = e.clientY;

            // Use requestAnimationFrame for smoother performance
            if (!freeRotationRafId) {
                freeRotationRafId = requestAnimationFrame(updateFreeRotation);
            }
        };

        const handleRotationEnd = () => {
            // Add fade-out animation to rotation container
            rotationContainer.style.transition = 'opacity 0.3s ease';
            rotationContainer.style.opacity = '0';

            // Remove rotation container after animation completes
            setTimeout(() => {
                if (document.body.contains(rotationContainer)) {
                    document.body.removeChild(rotationContainer);
                }
            }, 300);

            // Remove event listeners
            document.removeEventListener('mousemove', handleRotationMove);
            document.removeEventListener('mouseup', handleRotationEnd);

            // Apply pending rotation to React state
            const element = document.querySelector(`[data-element-id="${id}"]`);
            if (element && element._pendingRotation !== undefined) {
                // Reset transform to ensure consistent positioning
                element.style.transform = '';

                // Update React state
                updateElement(id, { rotation: element._pendingRotation });
                delete element._pendingRotation;

                // Reset willChange to improve performance when not moving
                element.style.willChange = 'auto';
            }
        };

        document.addEventListener('mousemove', handleRotationMove);
        document.addEventListener('mouseup', handleRotationEnd);
    };

    const handleElementClick = (element, e) => {
        e.stopPropagation();
        
        // Get the design space container
        const designSpace = designSpaceRef.current;
        const designSpaceRect = designSpace.getBoundingClientRect();
        
        // Get the element's position and dimensions
        const elementRect = e.currentTarget.getBoundingClientRect();
        const elementTop = elementRect.top - designSpaceRect.top;
        const elementRight = designSpaceRect.right - elementRect.right;
        const elementLeft = elementRect.left - designSpaceRect.left;
        const elementWidth = elementRect.width;
        const elementHeight = elementRect.height;
        
        // Define the threshold for edge detection (in pixels)
        const edgeThreshold = 50;
        
        // Define the distance between toolbar and element
        const toolbarDistance = 60;
        
        // Determine if element is near edges
        const isNearTop = elementTop < edgeThreshold;
        const isNearRight = elementRight < edgeThreshold;
        const isNearLeft = elementLeft < edgeThreshold;
        
        // Calculate toolbar position
        let position = {
            position: 'absolute',
            display: 'flex',
            zIndex: 1000,
            pointerEvents: 'auto'
        };
    
        // Calculate toolbar position based on element dimensions and position
        if (isNearRight) {
            // Position toolbar on the left side
            position = {
                ...position,
                left: `-${toolbarDistance}px`,
                top: `${elementHeight / 2}px`,
                transform: 'translateY(-50%)',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center'
            };
        } else if (isNearLeft) {
            // Position toolbar on the right side with vertical alignment
            position = {
                ...position,
                left: `${elementWidth + 20}px`,
                top: `${elementHeight / 2}px`,
                transform: 'translateY(-50%)',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center'
            };
        } else {
            // Default position - above the element
            position = {
                ...position,
                top: `-${toolbarDistance}px`,
                left: `${elementWidth / 2}px`,
                transform: 'translateX(-50%)',
                flexDirection: 'row',
                alignItems: 'center'
            };
        }
    
        // If near top edge and not near sides, position below
        if (isNearTop && !isNearRight && !isNearLeft) {
            position = {
                ...position,
                top: 'auto',
                bottom: `-${toolbarDistance}px`,
                left: `${elementWidth / 2}px`,
                transform: 'translateX(-50%)',
                flexDirection: 'row',
                alignItems: 'center'
            };
        }
    
        // Update toolbar position state
        setToolbarPosition(position);
        
        // Update toolbar classes
        let classes = '';
        if (isNearTop) classes += ' top-edge';
        if (isNearRight) classes += ' right-edge';
        if (isNearLeft) classes += ' left-edge';
        setToolbarClasses(classes.trim());
        
        // Update selection
        setSelectedElement(element);
        if (e.ctrlKey || e.shiftKey || isMultiSelectActive) {
            setSelectedIds((prevSelectedIds) =>
                prevSelectedIds.includes(element.id)
                    ? prevSelectedIds.filter((selectedId) => selectedId !== element.id)
                    : [...prevSelectedIds, element.id]
            );
        } else {
            setSelectedIds([element.id]);
        }
    };

    // Function to get the position of resize handles
    const getResizeHandlePosition = (corner) => {
        switch (corner) {
            case "top-left":
                return { top: "-5px", left: "-5px" };
            case "top-right":
                return { top: "-5px", right: "-5px" };
            case "bottom-left":
                return { bottom: "-5px", left: "-5px" };
            case "bottom-right":
                return { bottom: "-5px", right: "-5px" };
            default:
                return {};
        }
    };

    const showAlignmentLines = (x, y, element, designSpaceRect) => {
        let vertical = null;
        let horizontal = null;

        // Tolerance for alignment
        const tolerance = 5;

        // Check alignment with container
        const containerCenterX = designSpaceRect.width / 2;
        const containerCenterY = designSpaceRect.height / 2;

        if (Math.abs(x + element.width / 2 - containerCenterX) < tolerance) {
            vertical = { position: containerCenterX, type: "solid" };
        }

        if (Math.abs(y + element.height / 2 - containerCenterY) < tolerance) {
            horizontal = { position: containerCenterY, type: "solid" };
        }

        // Check alignment with other elements
        elements.forEach((el) => {
            if (el.id !== element.id) {
                // Horizontal alignment checks
                if (Math.abs(el.y - y) < tolerance) {
                    horizontal = { position: el.y, type: "dotted" }; // Top alignment
                } else if (Math.abs(el.y + el.height - y) < tolerance) {
                    horizontal = { position: el.y + el.height, type: "dotted" }; // Bottom aligns with top
                } else if (Math.abs(el.y + el.height / 2 - (y + element.height / 2)) < tolerance) {
                    horizontal = { position: el.y + el.height / 2, type: "dotted" }; // Center horizontally
                }

                // Vertical alignment checks
                if (Math.abs(el.x - x) < tolerance) {
                    vertical = { position: el.x, type: "dotted" }; // Left alignment
                } else if (Math.abs(el.x + el.width - x) < tolerance) {
                    vertical = { position: el.x + el.width, type: "dotted" }; // Right aligns with left
                } else if (Math.abs(el.x + el.width / 2 - (x + element.width / 2)) < tolerance) {
                    vertical = { position: el.x + el.width / 2, type: "dotted" }; // Center vertically
                }
            }
        });

        setAlignmentLines({ vertical, horizontal });
    };

    // Check if selected element is an image to show image sidebar
    useEffect(() => {
        if (selectedIds.length === 1) {
            const selectedElement = elements.find(el => el.id === selectedIds[0]);
            if (selectedElement && selectedElement.type === 'img') {
                setShowImageSidebar(true);
            } else {
                setShowImageSidebar(false);
            }
        } else {
            setShowImageSidebar(false);
        }
    }, [selectedIds, elements]);

    // Toggle AI Tools panel
    const toggleAITools = () => {
        setShowAITools(prev => !prev);

        // If we're opening AI Tools, make sure we have a selected image
        // or we're in generate mode
        if (!showAITools && selectedIds.length === 0) {
            // No image selected, we'll be in generate mode by default
            console.log("Opening AI Tools in generate mode");
        }
    };

    // Prevent clicks inside the image sidebar or AI tools from deselecting the element
    const handleMainClick = (e) => {
        // Check if the click is inside the image sidebar or AI tools
        const isImageSidebarClick = e.target.closest('.image-edit-sidebar');
        const isAIToolsClick = e.target.closest('.ai-tools-tabs') || e.target.closest('.ai-tools-hub');
        const isAIToolsButton = e.target.closest('button') && (
            e.target.closest('button').textContent.includes('Apply Style') ||
            e.target.closest('button').textContent.includes('Enhance Image') ||
            e.target.closest('button').textContent.includes('Change Background')
        );

        // Don't deselect if clicking in sidebars, AI tools, or specific buttons
        if (!isImageSidebarClick && !isAIToolsClick && !isAIToolsButton) {
            setSelectedIds([]);
        }
    };

    // Handle keyboard events for selected elements
    useEffect(() => {
        const handleKeyDown = (e) => {
            // Only process if we have selected elements
            if (selectedIds.length === 0) return;

            // Delete key - delete selected elements
            if (e.key === 'Delete' || e.key === 'Backspace') {
                // Filter out the selected elements
                const updatedElements = elements.filter(elem => !selectedIds.includes(elem.id));

                // Update the elements array
                setElements(updatedElements);

                // Clear selection
                setSelectedIds([]);

                // Prevent default behavior (like browser back with backspace)
                e.preventDefault();
            }

            // Arrow keys - move selected elements
            const moveDistance = e.shiftKey ? 10 : 1; // Move 10px with Shift key, 1px without

            if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(e.key)) {
                e.preventDefault(); // Prevent scrolling

                // Create a copy of elements to update
                const updatedElements = [...elements];

                // Update position of each selected element
                selectedIds.forEach(id => {
                    const elementIndex = updatedElements.findIndex(el => el.id === id);
                    if (elementIndex !== -1) {
                        const element = { ...updatedElements[elementIndex] };

                        // Update position based on arrow key
                        switch (e.key) {
                            case 'ArrowUp':
                                element.y = Math.max(0, element.y - moveDistance);
                                break;
                            case 'ArrowDown':
                                element.y = Math.min(cardType.height - element.height, element.y + moveDistance);
                                break;
                            case 'ArrowLeft':
                                element.x = Math.max(0, element.x - moveDistance);
                                break;
                            case 'ArrowRight':
                                element.x = Math.min(cardType.width - element.width, element.x + moveDistance);
                                break;
                            default:
                                break;
                        }

                        // Update element in the array
                        updatedElements[elementIndex] = element;
                    }
                });

                // Update elements state
                setElements(updatedElements);
            }

            // Copy with Ctrl+C
            if (e.key === 'c' && (e.ctrlKey || e.metaKey)) {
                e.preventDefault();

                // Get selected elements
                const selectedElements = elements.filter(el => selectedIds.includes(el.id));

                // Store in clipboard
                setClipboard(selectedElements);

                // Show feedback
                console.log('Elements copied to clipboard');
            }

            // Paste with Ctrl+V
            if (e.key === 'v' && (e.ctrlKey || e.metaKey)) {
                e.preventDefault();

                if (clipboard.length > 0) {
                    // Create a copy of elements
                    const updatedElements = [...elements];
                    const newSelectedIds = [];

                    // Add offset for pasted elements
                    const pasteOffset = 20; // 20px offset

                    // Add clipboard elements with new IDs
                    clipboard.forEach(el => {
                        const newId = `element-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
                        const newElement = {
                            ...el,
                            id: newId,
                            x: Math.min(el.x + pasteOffset, cardType.width - el.width),
                            y: Math.min(el.y + pasteOffset, cardType.height - el.height)
                        };

                        updatedElements.push(newElement);
                        newSelectedIds.push(newId);
                    });

                    // Update elements and selection
                    setElements(updatedElements);
                    setSelectedIds(newSelectedIds);

                    // Show feedback
                    console.log('Elements pasted from clipboard');
                }
            }
        };

        // Add event listener
        document.addEventListener('keydown', handleKeyDown);

        // Clean up
        return () => {
            document.removeEventListener('keydown', handleKeyDown);
        };
    }, [selectedIds, elements, setElements, setSelectedIds, cardType.width, cardType.height, clipboard]);

    // حساب موضع ColorPicker بجانب منطقة التصميم
    useEffect(() => {
        if (colorPickerTargetId && designSpaceRef.current) {
            const rect = designSpaceRef.current.getBoundingClientRect();
            setColorPickerPosition({
                top: rect.top,
                left: rect.right + 32 // مسافة أكبر بين القائمة ومنطقة التصميم
            });
        }
        const handleResize = () => {
            if (colorPickerTargetId && designSpaceRef.current) {
                const rect = designSpaceRef.current.getBoundingClientRect();
                setColorPickerPosition({
                    top: rect.top,
                    left: rect.right + 32 // مسافة أكبر بين القائمة ومنطقة التصميم
                });
            }
        };
        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, [colorPickerTargetId, designSpaceRef]);

    return (
        <div className={`flex ${isMobile ? 'flex-col' : 'flex-col'} relative w-full items-start h-full`} onClick={handleMainClick}> {/*should probably refractor it*/}
            {/* Top Toolbar - Dark Background - Hide on mobile */}
            {!isMobile && (
                <div className="w-full bg-gray-800 text-white p-2">
                    {/* Placeholder for top toolbar */}
                </div>
            )}

            {/* Canva Toolbar with Save Button - Hide on mobile */}
            {!isMobile && (
                <CanvaToolbar
                    updateTemplateData={updateTemplateData}
                />
            )}

            {/* Second Toolbar */}
            <div className={`w-full flex items-center p-2 bg-gray-50 border-b border-gray-200 ${isMobile ? 'flex-nowrap gap-2' : ''}`}>
                {/* Left: Back Button */}
                <div className="flex items-center flex-shrink-0">
                    <motion.button
                        className="flex items-center px-3 py-1.5 rounded-md bg-gradient-to-r from-gray-800 to-gray-700 text-white shadow-sm"
                        style={{ minWidth: 70 }}
                        onClick={() => window.history.back()}
                        whileHover={{
                            scale: 1.05,
                            boxShadow: "0 4px 8px rgba(0,0,0,0.2)"
                        }}
                        whileTap={{ scale: 0.95 }}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.3 }}
                    >
                        <motion.span
                            animate={{ x: [0, -3, 0] }}
                            transition={{ duration: 1.5, repeat: Infinity, repeatType: "loop" }}
                        >
                            <FiChevronLeft size={18} />
                        </motion.span>
                        <span className="ml-1 font-medium">Back</span>
                    </motion.button>
                </div>

                {/* Center: Card Type Dropdown (mobile only) */}
                {isMobile && (
                    <div className="flex-1 flex justify-center">
                        <TypeControl hideLabel={true} />
                    </div>
                )}

                {/* Right: Other Controls (includes TypeControl on web) */}
                <div className="flex items-center flex-shrink-0 ml-auto">
                    {!isMobile && <TypeControl />}
                    {!isMobile && <AlignmentControl />}
                    {!isMobile && <ResizeInputs />}
                    {!isMobile && <DuplicateControl />}
                    {!isMobile && <DeleteControl />}
                    <button
                        className="ml-2 p-2 rounded-full bg-gradient-to-r from-blue-500 to-cyan-500 text-white shadow-lg hover:from-blue-600 hover:to-cyan-600 flex items-center justify-center border-2 border-white/70"
                        style={{ boxShadow: '0 4px 16px 0 rgba(0, 180, 255, 0.15)' }}
                        onClick={() => document.querySelector('.help-guide-button').click()}
                        title="Help"
                    >
                        <FiHelpCircle size={22} className="drop-shadow" />
                    </button>
                    {!isMobile && (
                        <button
                            className="ml-2 p-2 rounded-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white shadow-sm hover:from-purple-700 hover:to-indigo-700 flex items-center justify-center"
                            onClick={toggleAITools}
                            title={showAITools ? "Close AI Image Generator" : "Open AI Image Generator"}
                        >
                            <FaRobot className="h-5 w-5" />
                        </button>
                    )}
                    {selectedIds.length === 1 && elements.find(el => el.id === selectedIds[0] && el.type === 'img') && (
                        <button
                            className="ml-2 p-2 rounded-full bg-gradient-to-r from-red-500 to-orange-500 text-white hover:from-red-600 hover:to-orange-600"
                            onClick={() => console.log('Remove background clicked')}
                            title="Remove Image Background"
                        >
                            <RiScissorsCutFill className="h-5 w-5" />
                        </button>
                    )}
                </div>
            </div>

            {/* Main Content Area - Responsive layout */}
            <div className={`flex ${isMobile ? 'flex-col' : 'flex-row'} w-full h-full overflow-hidden`}>
                {/* Left Sidebar - Desktop only */}
                {!isMobile && <LeftSidebar isMobile={isMobile} />}

                {/* Canvas Area */}
                <div className={`flex-grow flex flex-col h-full ${isMobile ? 'pb-24' : ''}`}>
                    {cardType && (
                        <div className={`flex-grow flex justify-center items-center ${isMobile ? 'p-2' : 'p-4'} overflow-auto relative design-space-container`}>
                            <DesignSpaceBackground />
                            <div
                                className={`design-space relative shadow-xl ${isMobile ? 'mobile-canvas' : ''} ${zoomLevel !== 100 ? 'no-interactive-zoom' : ''}`}
                                ref={designSpaceRef}
                                style={{
                                    transformOrigin: 'center center',
                                    transition: 'transform 0.2s ease',
                                    ...(isMobile && {
                                        transform: 'scale(0.8)',
                                        maxWidth: 'calc(100vw - 16px)',
                                        maxHeight: 'calc(100vh - 200px)'
                                    })
                                }}
                            >
                                <div
                                    id="design-space-content"
                                    onContextMenu={(e) => e.preventDefault()}
                                    style={{
                                        width: `${cardType?.width}px`,
                                        height: `${cardType?.height}px`,
                                        position: "relative",
                                        backgroundColor: "transparent",
                                        backgroundImage: 'none',
                                        boxShadow: '0 0 40px rgba(0, 0, 0, 0.25)',
                                        backgroundSize: canvasBackgroundStyle?.backgroundSize,
                                        backgroundBlendMode: 'normal',
                                        backgroundPosition: canvasBackgroundStyle?.backgroundPosition || 'center',
                                        backgroundRepeat: canvasBackgroundStyle?.backgroundRepeat || 'repeat',
                                        opacity: 1
                                    }}
                                >
                                    {/* Professional corner marks */}
                                    <div className="corner-mark top-left"></div>
                                    <div className="corner-mark top-right"></div>
                                    <div className="corner-mark bottom-left"></div>
                                    <div className="corner-mark bottom-right"></div>
                                    {elements?.map((el) => (
                                        <div
                                            key={el.id}
                                            data-element-id={el.id}
                                            style={{
                                                position: "absolute",
                                                top: el.y,
                                                left: el.x,
                                                width: el.width,
                                                height: el.height,
                                                cursor: "move",
                                                zIndex: el.zIndex || 0,
                                                transform: `${el.rotation ? `rotate(${el.rotation}deg)` : ''}`,
                                                transformOrigin: 'center center',
                                                ...(el.style || {}) // Apply any custom styles like filters
                                            }}
                                            className={`draggable-element ${selectedIds.includes(el.id) ? 'selected' : ''}`}
                                            onMouseDown={(e) => handleMouseDown(e, el.id)}
                                            onClick={(e) => handleElementClick(el, e)}
                                        >
                                            <Element el={el} userData={design?.userData || {}} />

                                            {selectedIds.includes(el.id) && (
                                                <>
                                                    {/* Element Controls */}
                                                    <div className={`element-controls element-controls-sm ${toolbarClasses}`} style={toolbarPosition}>
                                                        {/* زر أيقونة لتغيير اللون بنفس شكل ColorPicker */}
                                                        <button
                                                            className="element-control-btn color-picker-btn"
                                                            title="تغيير اللون"
                                                            onClick={() => setColorPickerTargetId(el.id)}
                                                        >
                                                            <MdOutlineColorLens size={18} />
                                                        </button>
                                                        <button
                                                            className="element-control-btn rotate-btn"
                                                            title="Rotate 90°"
                                                            onClick={() => {
                                                                const currentRotation = el.rotation || 0;
                                                                updateElement(el.id, { rotation: currentRotation + 90 });
                                                            }}
                                                        >
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" viewBox="0 0 16 16">
                                                                <path d="M8 3a5 5 0 1 0 4.546 2.914.5.5 0 0 1 .908-.417A6 6 0 1 1 8 2v1z"/>
                                                                <path d="M8 4.466V.534a.25.25 0 0 1 .41-.192l2.36 1.966c.12.1.12.284 0 .384L8.41 4.658A.25.25 0 0 1 8 4.466z"/>
                                                            </svg>
                                                        </button>
                                                        <button
                                                            className="element-control-btn forward-btn"
                                                            title="Bring Forward"
                                                            onClick={() => bringToFront(el.id)}
                                                        >
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" viewBox="0 0 16 16">
                                                                <path d="M8 6.5a.5.5 0 0 1 .5.5v1.5H10a.5.5 0 0 1 0 1H8.5V11a.5.5 0 0 1-1 0V9.5H6a.5.5 0 0 1 0-1h1.5V7a.5.5 0 0 1 .5-.5z"/>
                                                                <path d="M0 8a8 8 0 1 1 16 0A8 8 0 0 1 0 8zm8-7a7 7 0 0 0-7 7 7 7 0 0 0 7 7 7 7 0 0 0 7-7 7 7 0 0 0-7-7z"/>
                                                            </svg>
                                                        </button>
                                                        <button
                                                            className="element-control-btn backward-btn"
                                                            title="Send Backward"
                                                            onClick={() => sendToBack(el.id)}
                                                        >
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" viewBox="0 0 16 16">
                                                                <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                                                                <path d="M4 8a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7A.5.5 0 0 1 4 8z"/>
                                                            </svg>
                                                        </button>
                                                        <button
                                                            className="element-control-btn duplicate-btn"
                                                            title="Duplicate"
                                                            onClick={() => {
                                                                // Create a deep copy of the element with a new ID and slightly offset position
                                                                const newElement = {
                                                                    ...JSON.parse(JSON.stringify(el)),
                                                                    id: `${el.id}-copy-${Date.now()}`,
                                                                    x: el.x + 20,
                                                                    y: el.y + 20
                                                                };
                                                                // Add the new element to the elements array
                                                                const updatedElements = [...elements, newElement];
                                                                setElements(updatedElements);
                                                                // Select only the new element
                                                                setSelectedIds([newElement.id]);
                                                            }}
                                                        >
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" viewBox="0 0 16 16">
                                                                <path d="M4 1.5H3a2 2 0 0 0-2 2V14a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V3.5a2 2 0 0 0-2-2h-1v1h1a1 1 0 0 1 1 1V14a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V3.5a1 1 0 0 1 1-1h1v-1z"/>
                                                                <path d="M9.5 1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-1a.5.5 0 0 1 .5-.5h3zm-3-1A1.5 1.5 0 0 0 5 1.5v1A1.5 1.5 0 0 0 6.5 4h3A1.5 1.5 0 0 0 11 2.5v-1A1.5 1.5 0 0 0 9.5 0h-3z"/>
                                                            </svg>
                                                        </button>
                                                        <button
                                                            className="element-control-btn delete-btn"
                                                            title="Delete"
                                                            onClick={() => {
                                                                // Filter out the current element
                                                                const updatedElements = elements.filter(elem => elem.id !== el.id);
                                                                // Update the elements array
                                                                setElements(updatedElements);
                                                                // Clear selection
                                                                setSelectedIds([]);
                                                                // Show a subtle animation effect when deleting
                                                                const elementToDelete = document.querySelector(`[data-element-id="${el.id}"]`);
                                                                if (elementToDelete) {
                                                                    elementToDelete.style.transition = 'all 0.2s ease';
                                                                    elementToDelete.style.transform = 'scale(0.8)';
                                                                    elementToDelete.style.opacity = '0';
                                                                }
                                                            }}
                                                        >
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" viewBox="0 0 16 16">
                                                                <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6z"/>
                                                                <path fillRule="evenodd" d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z"/>
                                                            </svg>
                                                        </button>
                                                    </div>

                                                    {/* Layer indicator */}
                                                    <div className="layer-indicator">
                                                        Layer {el.zIndex || 0}
                                                    </div>

                                                    {/* Mode indicator */}
                                                    {isRotationMode && (
                                                        <div className="mode-indicator">
                                                            Rotation Mode
                                                        </div>
                                                    )}

                                                    {/* Rotation Handle */}
                                                    <div
                                                        className="rotation-handle"
                                                        onMouseDown={(e) => handleRotationStart(e, el.id)}
                                                    />

                                                    {/* Resize/Rotate Handles */}
                                                    {["top-left", "top-right", "bottom-left", "bottom-right"].map((corner) => (
                                                        <div
                                                            key={corner}
                                                            className={`resize-handle`}
                                                            style={{
                                                                cursor: `${["top-left", "bottom-right"].includes(corner) ? "nwse-resize" : "nesw-resize"}`,
                                                                ...getResizeHandlePosition(corner),
                                                            }}
                                                            onMouseDown={(e) => {
                                                                e.preventDefault(); // Prevent default behavior
                                                                    handleMouseDown(e, el.id, corner);
                                                            }}
                                                            onContextMenu={(e) => e.preventDefault()} // Prevent context menu
                                                            title="Drag to resize"
                                                        />
                                                    ))}
                                                </>
                                            )}
                                        </div>
                                    ))}
                                    <AlignmentContainer alignmentLines={alignmentLines} />
                                </div>
                            </div>
                        </div>
                    )}
                </div>

                {/* Image Edit Sidebar */}
                {showImageSidebar && (
                    <div className="w-2/12 border-l border-gray-200 bg-white shadow-lg"
                         style={{ height: '100%' }}>
                        <ImageEditSidebar />
                    </div>
                )}

                {/* AI Tools Sidebar */}
                {showAITools && (
                    <div className="w-3/12 border-l border-gray-200 bg-white shadow-lg"
                         style={{ height: 'calc(100vh - 120px)', overflowY: 'auto' }}>
                        <div className="sticky top-0 p-3 border-b border-gray-200 flex items-center justify-between bg-gray-50 z-10">
                            <h3 className="text-lg font-medium text-gray-800 flex items-center">
                                <FaRobot className="mr-2 text-purple-600" size={18} />
                                AI Tools
                            </h3>
                            <button
                                className="text-gray-500 hover:text-gray-700 p-1 rounded-full"
                                onClick={toggleAITools}
                            >
                                <FiChevronRight size={20} />
                            </button>
                        </div>
                        <div className="p-4">
                            <AIToolsHub />
                        </div>
                    </div>
                )}
            </div>

            {/* Mobile Bottom Sidebar - Canva-like design */}
            {isMobile && (
                <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg mobile-toolbar-backdrop z-50">
                    <LeftSidebar isMobile={isMobile} />
                </div>
            )}

            {/* Mobile Zoom Controls */}
            {isMobile && (
                <div className="mobile-zoom-controls">
                    <button
                        className="mobile-zoom-btn"
                        onClick={() => {
                            const canvas = document.querySelector('.design-space');
                            if (canvas) {
                                const currentScale = parseFloat(canvas.style.transform.match(/scale\(([\d.]+)\)/)?.[1] || '0.8');
                                const newScale = Math.min(currentScale + 0.1, 1.5);
                                canvas.style.transform = `scale(${newScale})`;
                            }
                        }}
                        title="Zoom In"
                    >
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <circle cx="11" cy="11" r="8"/>
                            <path d="M21 21l-4.35-4.35"/>
                            <line x1="11" y1="8" x2="11" y2="14"/>
                            <line x1="8" y1="11" x2="14" y2="11"/>
                        </svg>
                    </button>
                    <button
                        className="mobile-zoom-btn"
                        onClick={() => {
                            const canvas = document.querySelector('.design-space');
                            if (canvas) {
                                const currentScale = parseFloat(canvas.style.transform.match(/scale\(([\d.]+)\)/)?.[1] || '0.8');
                                const newScale = Math.max(currentScale - 0.1, 0.3);
                                canvas.style.transform = `scale(${newScale})`;
                            }
                        }}
                        title="Zoom Out"
                    >
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <circle cx="11" cy="11" r="8"/>
                            <path d="M21 21l-4.35-4.35"/>
                            <line x1="8" y1="11" x2="14" y2="11"/>
                        </svg>
                    </button>
                    <button
                        className="mobile-zoom-btn"
                        onClick={() => {
                            const canvas = document.querySelector('.design-space');
                            if (canvas) {
                                canvas.style.transform = 'scale(0.8)';
                            }
                        }}
                        title="Reset Zoom"
                    >
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M1 4v6h6"/>
                            <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10"/>
                        </svg>
                    </button>
                </div>
            )}

            {/* Help Guide */}
            <HelpGuide />

            {/* ColorPicker العائم بجانب منطقة التصميم مباشرة */}
            {colorPickerTargetId && (
                <div style={{
                    position: 'fixed',
                    top: colorPickerPosition.top,
                    left: colorPickerPosition.left,
                    zIndex: 9999
                }}>
                    <ColorPicker
                        elementId={colorPickerTargetId}
                        open={true}
                        onClose={() => setColorPickerTargetId(null)}
                    />
                </div>
            )}
        </div>
    );
};

DesignSpace.propTypes = {
    updateTemplateData: PropTypes.func,
    design: PropTypes.shape({
        userData: PropTypes.object
    })
};

export default DesignSpace;
