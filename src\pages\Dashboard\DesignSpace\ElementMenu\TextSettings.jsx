import { useMemo } from "react";
import { v4 as uuidv4 } from 'uuid';

import { Lu<PERSON><PERSON><PERSON>ower, LuCaseSensitive, LuCaseUpper } from "react-icons/lu";
import { FaBold, FaItalic, FaUnderline } from "react-icons/fa";
import { IoPricetagsOutline } from "react-icons/io5";
import { Dropdown } from 'primereact/dropdown';

import { fontFamilyOptions, fontSizesOptions, alignmentsOptions, colorsOptions } from "@constants";
import { useDesignSpace } from "@contexts/DesignSpaceContext";
import FieldsDropdown from "./FieldsDropdown";


function TextSettings() {
  const { addElement, elements, setElements, setSelectedElement, selectedElement } = useDesignSpace();

  const textSettingHandler = (e, key, value) => {
    e.stopPropagation()
    if (!selectedElement) return;

    setSelectedElement(prev => ({ ...prev, [key]: value }))
    const updatedElements = elements.map((el) =>
      el.id === selectedElement.id
        ? {
          ...el,
          [key]: value,
        }
        : el
    );
    setElements(updatedElements);
  };

  const isText = useMemo(() => selectedElement?.type === "text", [selectedElement])

  return (
    <>
      <div className="flex justify-start">
        <button className="w-full me-1 my-2 add-element-btn" onClick={() => addElement("label")}>
          <IoPricetagsOutline size={24} className="mb-2" />
          <span>Add Label</span>
        </button>
      </div>

      <div className="flex flex-col justify-start">
        <h4 className="font-bold mt-4 mb-2 text-[#676666]">Add Filed</h4>
        <div className="flex items-center mb-3">
          <FieldsDropdown />
        </div>
      </div>

      <div className="flex flex-col justify-start">
        <h4 className="font-bold mt-4 mb-2 text-[#676666]">Edit Text</h4>

        <div className="flex items-center mb-5">
          {/* font family */}
          <Dropdown
            value={isText ? selectedElement?.fontFamily : ""}
            options={fontFamilyOptions}
            onChange={(e) => { textSettingHandler(e, "fontFamily", e.value) }}
            optionLabel="label"
            optionValue="value"
            placeholder="font"
            className={`w-6/12 me-1`}
          />

          {/* font size */}
          <Dropdown
            value={isText ? selectedElement?.fontSize : ""}
            options={fontSizesOptions}
            onClick={(e) => { e.stopPropagation() }}
            onChange={(e) => { textSettingHandler(e, "fontSize", e.value) }}
            optionLabel="label"
            optionValue="value"
            placeholder="size"
            className={`w-6/12 mx-1`}
          />
        </div>

        <div className="flex items-center mb-4">
          {/* alignment */}
          <div className="w-6/12 flex justify-center items-center">
            {
              alignmentsOptions?.map((option, index) => {
                return (
                  <button key={`${index}_${uuidv4}`}
                    className={`w-3/12 p-2 mx-1 ${selectedElement?.textAlign === option.value ? "active-bh" : ""}`}
                    onClick={(e) => { textSettingHandler(e, "textAlign", option?.value) }}
                  >
                    {option?.icon}
                  </button>)
              })
            }
          </div>

          <div className="w-6/12 flex justify-evenly items-center">
            <button
              className={`w-3/12 p-2 mx-1 ${selectedElement?.isBold ? "active-bh" : ""}`}
              onClick={(e) => { textSettingHandler(e, "isBold", !selectedElement?.isBold) }}
            >
              <FaBold className="mx-auto" />
            </button>

            {/* Italic */}
            <button
              className={`w-3/12 p-2 mx-1 ${selectedElement?.isItalic ? "active-bh" : ""}`}
              onClick={(e) => { textSettingHandler(e, "isItalic", !selectedElement?.isItalic) }}
            >
              <FaItalic className="mx-auto" />
            </button>
            {/* text decoration */}
            <button className={`w-3/12 p-2 mx-1 ${selectedElement?.isUnderlined ? "active-bh" : ""}`}
              onClick={(e) => { textSettingHandler(e, "isUnderlined", !selectedElement?.isUnderlined) }}>
              <FaUnderline className="mx-auto" />
            </button>
          </div>
        </div>
        <div className="flex items-center mb-4">
          {/* colors */}
          <div className="w-6/12 flex justify-between items-center">
            {
              colorsOptions?.map((option, index) => {
                return (
                  <button key={`${index}_${uuidv4}`}
                    className={`w-2/12 p-3 mx-1 me-1 shadow-lg rounded-[3px] ${selectedElement?.color === option ? "border-2 border-[#00c7b1] scale-110" : ""}`}
                    style={{
                      backgroundColor: option,
                      border: option === "#ffffff" ? "1.5px solid #d1d5db" : undefined,
                      boxShadow: option === "#ffffff" ? "0 0 0 2px #e5e7eb" : undefined
                    }}
                    onClick={(e) => { textSettingHandler(e, "color", option) }}
                  >
                    {/* يمكن ترك المحتوى فارغًا أو وضع دائرة صغيرة أو علامة صح عند التحديد */}
                  </button>)
              })
            }
          </div>

          {/* letter case */}
          <div className="w-6/12 flex justify-between items-center">
            <button
              className={`w-3/12 p-1 mx-1 ${selectedElement?.textTransform === "capitalize" ? "active-bh" : ""}`}
              onClick={(e) => { textSettingHandler(e, "textTransform", "capitalize") }}
            >
              <LuCaseSensitive className="mx-auto" size={30} />
            </button>

            <button
              className={`w-3/12 p-1 mx-1 ${selectedElement?.textTransform === "uppercase" ? "active-bh" : ""}`}
              onClick={(e) => { textSettingHandler(e, "textTransform", "uppercase") }}
            >
              <LuCaseUpper className="mx-auto" size={30} />
            </button>

            <button className={`w-3/12 p- mx-1 ${selectedElement?.textTransform === "lowercase" ? "active-bh" : ""}`}
              onClick={(e) => { textSettingHandler(e, "textTransform", "lowercase") }}>
              <LuCaseLower className="mx-auto" size={30} />
            </button>
          </div>
        </div>
      </div>
    </>
  )
}

export default TextSettings